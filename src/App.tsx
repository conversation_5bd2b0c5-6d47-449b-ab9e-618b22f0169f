
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import {
  AuthLogin,
  ProductsList,
  ProductsDashboard,
  ProductsNotFound,
  CreateTenantOnboarding
} from "./pages";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Navigate to="/auth/login" replace />} />
          <Route path="/auth/login" element={<AuthLogin />} />
          <Route path="/products" element={<ProductsList />} />
          <Route path="/onboarding/create" element={<CreateTenantOnboarding />} />
          <Route path="/onboarding/create/:tenantId" element={<CreateTenantOnboarding />} />
          <Route path="/dashboard" element={<ProductsDashboard />} />
          <Route path="/dashboard/:slug" element={<ProductsDashboard />} />
          <Route path="*" element={<ProductsNotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
