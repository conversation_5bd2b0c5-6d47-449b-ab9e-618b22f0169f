import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { authService, CreateTenantData, Tenant } from '@/services';
import { toast } from '@/hooks/use-toast';
import {
  TenantSelectForm,
  BusinessForm,
  SlugForm,
  RoleForm,
  CreatingForm,
  SuccessForm
} from './components';

type OnboardingStep = 'tenant-select' | 'business' | 'slug' | 'role' | 'creating' | 'success';

const CreateTenantOnboarding: React.FC = () => {
  const [currentStep, setCurrentStep] = useState<OnboardingStep>('tenant-select');
  const [loading, setLoading] = useState(false);
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [selectedTenant, setSelectedTenant] = useState<Tenant | null>(null);
  const [businessName, setBusinessName] = useState('');
  const [companySlug, setCompanySlug] = useState('');
  const [userName, setUserName] = useState('');
  const [userRole, setUserRole] = useState('');
  const [invitationLink, setInvitationLink] = useState('');
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const navigate = useNavigate();
  const { tenantId } = useParams();

  useEffect(() => {
    // Check if user is authenticated and is admin
    if (!authService.isAuthenticated() || !authService.isAdmin()) {
      navigate('/auth/login');
      return;
    }

    // If tenantId is provided in URL, skip tenant selection
    if (tenantId) {
      transitionToStep('business');
      fetchTenantDetails();
    } else {
      fetchTenants();
    }
  }, [navigate, tenantId]);

  const fetchTenants = async () => {
    try {
      const tenantList = await authService.getTenants();
      setTenants(tenantList);
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message,
      });
    }
  };

  const fetchTenantDetails = async () => {
    if (!tenantId) return;
    try {
      const tenant = await authService.getTenantDetails(tenantId);
      setSelectedTenant(tenant);
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message,
      });
    }
  };

  // Smooth step transition function
  const transitionToStep = (nextStep: OnboardingStep) => {
    setIsTransitioning(true);
    setTimeout(() => {
      setCurrentStep(nextStep);
      setIsTransitioning(false);
    }, 300);
  };

  const handleTenantSelect = (tenant: Tenant) => {
    setSelectedTenant(tenant);
    transitionToStep('business');
  };

  const handleBusinessNext = (name: string) => {
    setBusinessName(name);
    transitionToStep('slug');
  };

  const handleSlugNext = (slug: string) => {
    setCompanySlug(slug);
    transitionToStep('role');
  };

  const handleRoleNext = (data: { name: string; role: string }) => {
    setUserName(data.name);
    setUserRole(data.role);
    transitionToStep('creating');
  };

  const handleCreateTenant = useCallback(async () => {
    // Prevent multiple calls
    if (isCreating || loading) {
      console.log('Create tenant call blocked - already in progress');
      return;
    }

    console.log('Starting tenant creation...');
    setIsCreating(true);
    setLoading(true);

    try {
      const tenantData: CreateTenantData = {
        business_name: businessName,
        company_slug: companySlug,
        product: '685d2c9bb23fc531edf6dd0c', // Default product ID
        username: userName || 'admin' // Use provided username or default to admin
      };

      const response = await authService.createTenant(tenantData);

      setInvitationLink(response.invitation_link);

      // Add a small delay to show the success animation
      setTimeout(() => {
        transitionToStep('success');
        setLoading(false);
        setIsCreating(false);
      }, 2000);

    } catch (error: any) {
      setLoading(false);
      setIsCreating(false);
      toast({
        variant: "destructive",
        title: "Creation Failed",
        description: error.message,
      });
      // Go back to role step on error
      transitionToStep('role');
    }
  }, [isCreating, loading, businessName, companySlug, userName]);

  const handleBackToTenantSelect = () => {
    transitionToStep('tenant-select');
  };

  const handleBackToBusiness = () => {
    transitionToStep('business');
  };

  const handleBackToSlug = () => {
    transitionToStep('slug');
  };

  const handleNavigateToProducts = () => {
    navigate('/products');
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'tenant-select':
        return (
          <TenantSelectForm
            tenants={tenants}
            onSelect={handleTenantSelect}
            onCreateNew={() => transitionToStep('business')}
            loading={loading}
          />
        );
      case 'business':
        return (
          <BusinessForm
            onNext={handleBusinessNext}
            onBack={tenantId ? undefined : handleBackToTenantSelect}
            selectedTenant={selectedTenant}
            loading={loading}
          />
        );
      case 'slug':
        return (
          <SlugForm
            businessName={businessName}
            onNext={handleSlugNext}
            onBack={handleBackToBusiness}
            loading={loading}
          />
        );
      case 'role':
        return (
          <RoleForm
            onNext={handleRoleNext}
            onBack={handleBackToSlug}
            loading={loading}
          />
        );
      case 'creating':
        return (
          <CreatingForm
            businessName={businessName}
            userName={userName}
            onCreateTenant={handleCreateTenant}
            loading={loading}
          />
        );
      case 'success':
        return (
          <SuccessForm
            invitationLink={invitationLink}
            businessName={businessName}
            companySlug={companySlug}
            userName={userName}
            userRole={userRole}
            onNavigate={handleNavigateToProducts}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background px-4">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmZmZmYiIGZpbGwtb3BhY2l0eT0iMC4xIj48Y2lyY2xlIGN4PSIzMCIgY3k9IjMwIiByPSIyIi8+PC9nPjwvZz48L3N2Zz4=')] opacity-20"></div>
      
      {/* Progress Indicator */}
      <div className="absolute top-8 left-1/2 transform -translate-x-1/2">
        <div className="flex items-center space-x-4">
          {['tenant-select', 'business', 'slug', 'role', 'creating', 'success'].map((step, index) => (
            <div key={step} className="flex items-center">
              <div className={`w-3 h-3 rounded-full transition-all duration-300 ${
                currentStep === step
                  ? 'bg-foreground scale-125'
                  : index < ['tenant-select', 'business', 'slug', 'role', 'creating', 'success'].indexOf(currentStep)
                    ? 'bg-foreground/60'
                    : 'bg-foreground/20'
              }`} />
              {index < 5 && (
                <div className={`w-8 h-0.5 mx-2 transition-all duration-300 ${
                  index < ['tenant-select', 'business', 'slug', 'role', 'creating', 'success'].indexOf(currentStep)
                    ? 'bg-foreground/60'
                    : 'bg-foreground/20'
                }`} />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Main Content */}
      <div className="relative max-w-2xl w-full">
        <div className={`glass-card rounded-2xl shadow-2xl border border-border p-8 transition-all duration-300 ${
          isTransitioning ? 'opacity-50 scale-95' : 'opacity-100 scale-100'
        }`}>
          {renderCurrentStep()}
        </div>
      </div>
    </div>
  );
};

export default CreateTenantOnboarding;
