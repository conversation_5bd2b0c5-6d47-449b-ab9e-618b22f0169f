import React from 'react';
import { useTheme } from 'next-themes';

interface LogoProps {
  variant?: 'favicon' | 'logo';
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

const Logo: React.FC<LogoProps> = ({ 
  variant = 'logo', 
  className = '', 
  size = 'md' 
}) => {
  const { theme, systemTheme } = useTheme();
  
  // Determine the current theme
  const currentTheme = theme === 'system' ? systemTheme : theme;
  const isDark = currentTheme === 'dark';
  
  // Size classes
  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-12 w-12',
    lg: 'h-16 w-16',
    xl: 'h-20 w-20'
  };
  
  // Get the appropriate image source
  const getImageSrc = () => {
    if (variant === 'favicon') {
      return isDark ? '/favicon-white.png' : '/favicon-black.png';
    } else {
      // For logo variant, always use the black logo with transparent background
      return '/final-logo-black.png';
    }
  };
  
  const getAltText = () => {
    return variant === 'favicon' ? 'Multi-Tenant System Icon' : 'Multi-Tenant System Logo';
  };
  
  return (
    <img
      src={getImageSrc()}
      alt={getAltText()}
      className={`${sizeClasses[size]} object-contain ${className}`}
    />
  );
};

export default Logo;
